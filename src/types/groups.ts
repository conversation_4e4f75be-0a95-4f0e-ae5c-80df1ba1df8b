// 基础响应类型
export interface ApiResponse<T = any> {
  code: number
  message: string
  data: T
}

export interface PaginatedResponse<T> {
  items: T[]
  total: number
  page: number
  size: number
}

// 分组列表响应
export interface GroupsResponse {
  groups: Group[]
  total: number
  page: number
  size: number
}

// 成员列表响应
export interface MembersResponse {
  members: GroupMember[]
  total: number
  page: number
  size: number
}

// 申请列表响应
export interface RequestsResponse {
  requests: JoinRequest[]
  total: number
  page: number
  size: number
}

// Topic列表响应
export interface TopicsResponse {
  topics: GroupTopic[]
  total: number
  page: number
  size: number
}

// 分组相关类型
export interface Group {
  id: string
  group_id: string
  name: string
  description: string
  creator_id: string
  status: 'active' | 'inactive' | 'archived'
  max_members: number
  member_count: number
  topic_count: number
  created_at: string
  updated_at: string
}

export interface GroupDetail extends Group {
  is_creator: boolean
}



// 成员相关类型
export interface GroupMember {
  id: string
  group_id: string
  user_id: string
  role: 'creator' | 'member'
  joined_at: string
  email: string
  display_name: string
}

// Topic相关类型
export interface GroupTopic {
  id: string
  group_id: string
  topic_name: string
  full_name: string
  creator_id: string
  created_at: string
}

// 权限相关类型
export interface TopicPermission {
  id: string
  user_id: string
  group_id: string
  full_topic: string
  can_pub: boolean
  can_sub: boolean
  updated_at: string
  email: string
  display_name: string
}

// 申请相关类型
export interface JoinRequest {
  id: string
  group_id: string
  user_id: string
  status: 'pending' | 'approved' | 'rejected'
  message: string
  created_at: string
  updated_at: string
  email: string
  display_name: string
  group_name: string
}

// 请求参数类型
export interface CreateGroupRequest {
  name: string
  description: string
  max_members: number
}

export interface UpdateGroupRequest {
  name?: string
  description?: string
  status?: 'active' | 'inactive' | 'archived'
  max_members?: number
}

export interface JoinGroupRequest {
  group_id: string
  message: string
}

export interface ReviewJoinRequest {
  status: 'approved' | 'rejected'
}

export interface CreateTopicRequest {
  topic_name: string
}

export interface SetPermissionRequest {
  user_id: string
  full_topic: string
  can_pub: boolean
  can_sub: boolean
}

// 查询参数类型
export interface GetGroupsParams {
  page?: number
  page_size?: number
  creator_id?: string
  status?: 'active' | 'inactive' | 'archived'
  search?: string
}

export interface GetMembersParams {
  page?: number
  page_size?: number
}

export interface GetRequestsParams {
  page?: number
  page_size?: number
  status?: 'pending' | 'approved' | 'rejected'
}

export interface GetPermissionsParams {
  fullTopic: string
  page?: number
  page_size?: number
}

// 组件Props类型
export interface GroupCardProps {
  group: Group
  onDelete?: (groupId: string) => void
  onEdit?: (group: Group) => void
}

export interface MemberListProps {
  groupId: string
  isCreator?: boolean
}

export interface TopicListProps {
  groupId: string
  isCreator?: boolean
}

export interface PermissionMatrixProps {
  groupId: string
  isCreator?: boolean
}

export interface JoinRequestListProps {
  groupId?: string
  showGroupName?: boolean
}

// 表单数据类型
export interface CreateGroupFormData {
  name: string
  description: string
  max_members: number
}

export interface JoinGroupFormData {
  group_id: string
  message: string
}

export interface CreateTopicFormData {
  topic_name: string
}

export interface BatchPermissionFormData {
  user_ids: string[]
  full_topic: string
  can_pub: boolean
  can_sub: boolean
}

// 权限矩阵数据类型
export interface PermissionMatrixData {
  userId: string
  displayName: string
  email: string
  role: 'creator' | 'member'
  permissions: Record<string, { can_pub: boolean; can_sub: boolean }>
}

// Topic数据相关类型
export interface TopicMessage {
  timestamp: string
  topic: string
  payload: string
  qos: number
  client_id: string
  group_id: string
}

export interface TopicDataResponse {
  messages: TopicMessage[]
  total: number
  page: number
  page_size: number
  start_time: string
  end_time: string
}

export interface GetTopicDataParams {
  topic: string
  pagesize?: number
  page?: number
  timerange?: string
  start_time?: string
  end_time?: string
}

export interface DeleteTopicDataParams {
  topic: string
  start_time: string
  end_time: string
}

export interface DeleteTopicDataResponse {
  topic: string
  deleted_count: number
  start_time: string
  end_time: string
}

// 统计数据类型
export interface GroupOverviewData {
  group: GroupDetail
  memberCount: number
  topicCount: number
  pendingRequests: number
  recentActivity: ActivityItem[]
}

export interface ActivityItem {
  id: string
  type: 'member_joined' | 'topic_created' | 'permission_changed' | 'request_submitted'
  description: string
  timestamp: string
  user?: string
}

// 预警相关类型
export interface AlertCondition {
  field: string
  operator: string
  value: string
  data_type: 'number' | 'string' | 'bool'
}



export interface NotificationConfig {
  enabled: boolean
  channels: string[]
  recipients: string[]
}

export interface AlertRule {
  id: string
  group_id: string
  topic: string
  rule_name: string
  rule_type: string
  description: string
  conditions: AlertCondition[]
  level: number
  notification: NotificationConfig
  enabled: boolean
  created_at: string
  updated_at: string
}

export interface AlertRecord {
  id: string
  rule_id: string
  rule_name: string
  group_id: string
  topic: string
  level: number
  trigger_data: string
  trigger_time: string
  created_at: string
}



// 预警请求参数类型
export interface CreateAlertRuleRequest {
  group_id: string
  topic: string
  rule_name: string
  rule_type: string
  description: string
  conditions: AlertCondition[]
  level: number
  notification: NotificationConfig
  enabled: boolean
}

export interface UpdateAlertRuleRequest {
  rule_name?: string
  rule_type?: string
  description?: string
  conditions?: AlertCondition[]
  level?: number
  notification?: NotificationConfig
  enabled?: boolean
}

export interface GetAlertRulesParams {
  page?: number
  page_size?: number
  group_id?: string
  topic?: string
  rule_type?: string
  enabled?: boolean
  level?: number
  search?: string
}

export interface GetAlertRecordsParams {
  page?: number
  page_size?: number
  group_id?: string
  topic?: string
  rule_id?: string
  level?: number
  start_time?: string
  end_time?: string
}

export interface TestAlertRuleRequest {
  [key: string]: any
}