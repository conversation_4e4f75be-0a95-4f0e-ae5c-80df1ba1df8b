import apiClient from '@/lib/api'

// API响应基础接口
interface ApiResponse<T = any> {
  success: boolean
  message: string
  data?: T
  error?: string
}

// 设备状态枚举
export type DeviceStatus = 'active' | 'inactive' | 'maintenance'

// 设备响应接口
export interface DeviceResponse {
  id: string
  device_type: string
  device_name: string
  serial_number: string
  imei_code: string
  status: DeviceStatus
  created_at: string
  updated_at: string
}

// 设备类型响应接口
export interface DeviceTypeResponse {
  id: string
  name: string
  description: string
  created_at: string
  updated_at: string
}

// 分页响应接口
export interface PaginatedResponse<T> {
  devices?: T[]
  device_types?: T[]
  total: number
  page: number
  size: number
}

// 获取设备列表查询参数
export interface GetDevicesParams {
  page?: number
  page_size?: number
  device_type?: string
  status?: DeviceStatus
  search?: string
}

// 获取设备类型列表查询参数
export interface GetDeviceTypesParams {
  page?: number
  page_size?: number
}

// 创建设备请求接口
export interface CreateDeviceRequest {
  device_type: string
  device_name: string
  serial_number: string
  imei_code: string
}

// 更新设备请求接口
export interface UpdateDeviceRequest {
  device_name?: string
  status?: DeviceStatus
}

// 创建设备类型请求接口
export interface CreateDeviceTypeRequest {
  name: string
  description: string
}

// 更新设备类型请求接口
export interface UpdateDeviceTypeRequest {
  name?: string
  description?: string
}

// 设备管理API服务类
export class DevicesAPI {
  /**
   * 获取设备列表
   */
  static async getDevices(params?: GetDevicesParams): Promise<ApiResponse<PaginatedResponse<DeviceResponse>>> {
    const response = await apiClient.get('/api/devices', { params })
    return response.data
  }

  /**
   * 获取指定设备信息
   */
  static async getDevice(id: string): Promise<ApiResponse<DeviceResponse>> {
    const response = await apiClient.get(`/api/devices/${id}`)
    return response.data
  }

  /**
   * 创建设备
   */
  static async createDevice(data: CreateDeviceRequest): Promise<ApiResponse<DeviceResponse>> {
    const response = await apiClient.post('/api/devices', data)
    return response.data
  }

  /**
   * 更新设备信息
   */
  static async updateDevice(id: string, data: UpdateDeviceRequest): Promise<ApiResponse<DeviceResponse>> {
    const response = await apiClient.put(`/api/devices/${id}`, data)
    return response.data
  }

  /**
   * 删除设备
   */
  static async deleteDevice(id: string): Promise<ApiResponse> {
    const response = await apiClient.delete(`/api/devices/${id}`)
    return response.data
  }
}

// 设备类型管理API服务类
export class DeviceTypesAPI {
  /**
   * 获取设备类型列表
   */
  static async getDeviceTypes(params?: GetDeviceTypesParams): Promise<ApiResponse<PaginatedResponse<DeviceTypeResponse>>> {
    const response = await apiClient.get('/api/device-types', { params })
    return response.data
  }

  /**
   * 获取指定设备类型信息
   */
  static async getDeviceType(id: string): Promise<ApiResponse<DeviceTypeResponse>> {
    const response = await apiClient.get(`/api/device-types/${id}`)
    return response.data
  }

  /**
   * 创建设备类型
   */
  static async createDeviceType(data: CreateDeviceTypeRequest): Promise<ApiResponse<DeviceTypeResponse>> {
    const response = await apiClient.post('/api/device-types', data)
    return response.data
  }

  /**
   * 更新设备类型信息
   */
  static async updateDeviceType(id: string, data: UpdateDeviceTypeRequest): Promise<ApiResponse<DeviceTypeResponse>> {
    const response = await apiClient.put(`/api/device-types/${id}`, data)
    return response.data
  }

  /**
   * 删除设备类型
   */
  static async deleteDeviceType(id: string): Promise<ApiResponse> {
    const response = await apiClient.delete(`/api/device-types/${id}`)
    return response.data
  }
}

// 导出类型
export type {
  ApiResponse,
}
