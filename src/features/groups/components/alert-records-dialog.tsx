import { useState, useEffect, useMemo } from 'react'
import { useQuery } from '@/hooks/use-api'
import { format } from 'date-fns'
import { zhCN } from 'date-fns/locale'
import {
  AlertTriangle,
  Clock,
  Download,
  RefreshCw,
  Search,
  X,
} from 'lucide-react'
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog'
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Badge } from '@/components/ui/badge'
import { Skeleton } from '@/components/ui/skeleton'
import { ScrollArea } from '@/components/ui/scroll-area'
import { DataTablePagination } from '@/components/ui/data-table-pagination'
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select'

import { toast } from 'sonner'
import { GroupsAPI, groupsQueryKeys } from '@/api/groups'
import type { GroupTopic, AlertRecord, GetAlertRecordsParams } from '@/types/groups'
import { Tooltip, TooltipContent, TooltipTrigger } from '@/components/ui/tooltip'

// 实际API响应的数据结构
interface AlertRecordsApiResponse {
  records: AlertRecord[]
  total: number
  page: number
  size: number
}

interface AlertRecordsDialogProps {
  topic: GroupTopic | null
  groupId: string
  open: boolean
  onOpenChange: (open: boolean) => void
}

export function AlertRecordsDialog({ 
  topic, 
  groupId, 
  open, 
  onOpenChange 
}: AlertRecordsDialogProps) {
  const [currentPage, setCurrentPage] = useState(1)
  const [pageSize] = useState(10)
  const [timeRange, setTimeRange] = useState('168h')
  const [levelFilter, setLevelFilter] = useState<string>('all')
  const [searchTerm, setSearchTerm] = useState('')

  // 使用 useMemo 计算时间范围，避免无限循环
  const timeRangeParams = useMemo(() => {
    const now = new Date()
    const endTime = now.toISOString()

    let startTime: string
    switch (timeRange) {
      case '1h':
        startTime = new Date(now.getTime() - 1 * 60 * 60 * 1000).toISOString()
        break
      case '6h':
        startTime = new Date(now.getTime() - 6 * 60 * 60 * 1000).toISOString()
        break
      case '24h':
        startTime = new Date(now.getTime() - 24 * 60 * 60 * 1000).toISOString()
        break
      case '168h': // 7天
        startTime = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000).toISOString()
        break
      case '720h': // 30天
        startTime = new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000).toISOString()
        break
      default:
        startTime = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000).toISOString()
    }

    return { start_time: startTime, end_time: endTime }
  }, [timeRange])

  // 构建查询参数
  const queryParams: GetAlertRecordsParams = {
    page: currentPage,
    page_size: pageSize,
    group_id: groupId,
    topic: topic?.full_name,
    ...(levelFilter && levelFilter !== 'all' && { level: Number(levelFilter) }),
    ...timeRangeParams,
  }

  // 获取预警记录列表
  const { data: recordsResponse, isLoading, refetch, error } = useQuery({
    queryKey: groupsQueryKeys.alertRecords(queryParams),
    queryFn: async () => {
      if (!topic?.full_name) return null
      const response = await GroupsAPI.getAlertRecords(queryParams)
      // 将API响应的 records 字段映射为 items 字段以符合 PaginatedResponse 接口
      const data = response.data as unknown as AlertRecordsApiResponse
      return {
        items: data.records || [],
        total: data.total || 0,
        page: data.page || 1,
        size: data.size || pageSize
      }
    },
    enabled: open && !!topic?.full_name,
  })

  const records = recordsResponse?.items || []
  const total = recordsResponse?.total || 0
  const totalPages = Math.ceil(total / pageSize)

  // 重置状态当对话框关闭时
  useEffect(() => {
    if (!open) {
      setCurrentPage(1)
      setSearchTerm('')
      setLevelFilter('all')
      setTimeRange('168h')
    }
  }, [open])

  // 当过滤条件改变时重置页码
  useEffect(() => {
    setCurrentPage(1)
  }, [timeRange, levelFilter])

  // 过滤记录
  const filteredRecords = records.filter((record: any) => {
    if (!searchTerm) return true
    const searchLower = searchTerm.toLowerCase()
    return (
      (record.message || '').toLowerCase().includes(searchLower) ||
      (record.trigger_value || '').toLowerCase().includes(searchLower) ||
      (record.topic_name || '').toLowerCase().includes(searchLower)
    )
  })

  const handlePageChange = (page: number) => {
    setCurrentPage(page)
  }

  const handleRefresh = () => {
    refetch()
    toast.success('数据已刷新')
  }

  const handleExport = () => {
    if (filteredRecords.length === 0) {
      toast.error('没有数据可导出')
      return
    }

    const csvContent = [
      ['触发时间', '消息', '预警级别', '触发值', 'Topic'].join(','),
      ...filteredRecords.map((record: any) => [
        record.timestamp,
        `"${(record.message || '').replace(/"/g, '""')}"`,
        getLevelText(record.level),
        `"${(record.trigger_value || '').replace(/"/g, '""')}"`,
        record.topic_name || '',
      ].join(','))
    ].join('\n')

    const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' })
    const link = document.createElement('a')
    const url = URL.createObjectURL(blob)
    link.setAttribute('href', url)
    link.setAttribute('download', `alert-records-${topic?.topic_name}-${Date.now()}.csv`)
    link.style.visibility = 'hidden'
    document.body.appendChild(link)
    link.click()
    document.body.removeChild(link)
    
    toast.success('数据导出成功')
  }

  const formatTimestamp = (timestamp: string) => {
    try {
      return format(new Date(timestamp), 'yyyy-MM-dd HH:mm:ss', { locale: zhCN })
    } catch {
      return timestamp
    }
  }

  const getLevelBadgeVariant = (level: number) => {
    switch (level) {
      case 1: return 'secondary'
      case 2: return 'default'
      case 3: return 'destructive'
      case 4: return 'destructive'
      case 5: return 'destructive'
      default: return 'outline'
    }
  }

  const getLevelText = (level: number) => {
    switch (level) {
      case 1: return '信息'
      case 2: return '警告'
      case 3: return '错误'
      case 4: return '严重'
      case 5: return '灾难'
      default: return '未知'
    }
  }



  if (!topic) return null

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-h-[90vh] max-w-[95vw] flex flex-col">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <AlertTriangle className="h-5 w-5" />
            预警记录查看
          </DialogTitle>
          <DialogDescription>
            查看Topic "{topic.topic_name}" 的预警记录和统计信息
          </DialogDescription>
        </DialogHeader>



        {/* 控制栏 */}
        <div className="flex flex-col gap-4 border-b pb-4">
          {/* 第一行：时间范围和操作按钮 */}
          <div className="flex items-center justify-between gap-4">
            <div className="flex items-center gap-2">
              <Clock className="h-4 w-4 text-gray-500" />
              <Select value={timeRange} onValueChange={setTimeRange}>
                <SelectTrigger className="w-32">
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="1h">最近1小时</SelectItem>
                  <SelectItem value="6h">最近6小时</SelectItem>
                  <SelectItem value="24h">最近1天</SelectItem>
                  <SelectItem value="168h">最近7天</SelectItem>
                  <SelectItem value="720h">最近30天</SelectItem>
                </SelectContent>
              </Select>
              
              <Select value={levelFilter} onValueChange={setLevelFilter}>
                <SelectTrigger className="w-24">
                  <SelectValue placeholder="级别" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">全部</SelectItem>
                  <SelectItem value="1">信息</SelectItem>
                  <SelectItem value="2">警告</SelectItem>
                  <SelectItem value="3">错误</SelectItem>
                  <SelectItem value="4">严重</SelectItem>
                  <SelectItem value="5">灾难</SelectItem>
                </SelectContent>
              </Select>
            </div>

            <div className="flex items-center gap-2">
              <Button variant="outline" size="sm" onClick={handleRefresh} disabled={isLoading}>
                <RefreshCw className={`h-4 w-4 ${isLoading ? 'animate-spin' : ''}`} />
              </Button>
              <Button variant="outline" size="sm" onClick={handleExport} disabled={filteredRecords.length === 0}>
                <Download className="h-4 w-4" />
                导出
              </Button>
            </div>
          </div>

          {/* 第二行：搜索框 */}
          <div className="relative">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
            <Input
              placeholder="搜索规则名称或触发数据..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="pl-10"
            />
            {searchTerm && (
              <Button
                variant="ghost"
                size="sm"
                className="absolute right-2 top-1/2 transform -translate-y-1/2 h-6 w-6 p-0"
                onClick={() => setSearchTerm('')}
              >
                <X className="h-4 w-4" />
              </Button>
            )}
          </div>
        </div>

        {/* 记录列表 */}
        <div className="flex-1 min-h-0">
          {error ? (
            <div className="flex items-center justify-center h-32 text-red-500">
              <p>加载数据失败: {error.message}</p>
            </div>
          ) : isLoading ? (
            <div className="space-y-4">
              {Array.from({ length: 5 }).map((_, i) => (
                <div key={i} className="flex items-center gap-4 p-4 border rounded">
                  <Skeleton className="h-4 w-32" />
                  <Skeleton className="h-4 w-24" />
                  <Skeleton className="h-4 flex-1" />
                  <Skeleton className="h-4 w-16" />
                </div>
              ))}
            </div>
          ) : filteredRecords.length === 0 ? (
            <div className="flex items-center justify-center h-32 text-gray-500">
              <div className="text-center">
                <AlertTriangle className="mx-auto h-12 w-12 text-gray-400 mb-4" />
                <p>{searchTerm ? '没有找到匹配的预警记录' : '暂无预警记录'}</p>
              </div>
            </div>
          ) : (
            <ScrollArea className="h-full" orientation="horizontal">
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead className="w-40">触发时间</TableHead>
                    <TableHead>消息</TableHead>
                    <TableHead className="w-20">级别</TableHead>
                    <TableHead className="w-24">触发值</TableHead>
                    <TableHead>Topic</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {filteredRecords.map((record: any, index: number) => (
                    <TableRow key={record.rule_id + '-' + index}>
                      <TableCell className="font-mono text-xs">
                        {formatTimestamp(record.timestamp)}
                      </TableCell>
                      <TableCell className="font-medium">
                        <Tooltip>
                          <TooltipTrigger asChild>
                            <div className="max-w-50 truncate cursor-default">
                              <div className="text-sm truncate">{record.message}</div>
                            </div>
                          </TooltipTrigger>
                          <TooltipContent className="max-w-xs break-words">
                            {record.message}
                          </TooltipContent>
                        </Tooltip>
                      </TableCell>
                      <TableCell>
                        <Badge variant={getLevelBadgeVariant(record.level)}>
                          {getLevelText(record.level)}
                        </Badge>
                      </TableCell>
                      <TableCell className="font-mono text-xs">
                        {record.trigger_value}
                      </TableCell>
                      <TableCell className="font-mono text-xs text-muted-foreground">
                        {record.topic_name}
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </ScrollArea>
          )}
        </div>

        {/* 分页 */}
        {!isLoading && !error && total > 0 && (
          <div className="border-t pt-4">
            <DataTablePagination
              mode="server"
              currentPage={currentPage}
              totalPages={totalPages}
              totalItems={total}
              pageSize={pageSize}
              onPageChange={handlePageChange}
              isLoading={isLoading}
            />
          </div>
        )}
      </DialogContent>
    </Dialog>
  )
}
