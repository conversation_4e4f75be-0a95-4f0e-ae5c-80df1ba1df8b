import { useState } from 'react'
import { useQuery, useMutation, useQueryClient } from '@/hooks/use-api'
import {
  Users,
  UserMinus,
  Crown,
  Mail,
  Calendar,
  MoreVertical,
} from 'lucide-react'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu'
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from '@/components/ui/alert-dialog'
import { toast } from 'sonner'
import { GroupsAPI, groupsQueryKeys } from '@/api/groups'
import { DataTable } from '@/components/custom_components/data-table'
import { SimpleColumn } from '@/components/simple-table'
import type { GroupMember } from '@/types/groups'

interface MemberManagementProps {
  groupId: string
  isCreator?: boolean
}

export function MemberManagement({ groupId, isCreator = false }: MemberManagementProps) {
  const queryClient = useQueryClient()
  const [removingMember, setRemovingMember] = useState<GroupMember | null>(null)

  // 获取成员列表
  const { data: membersData, isLoading } = useQuery({
    queryKey: groupsQueryKeys.members(groupId),
    queryFn: async () => {
      const response = await GroupsAPI.getMembers(groupId)
      return response.data
    },
  })

  // 移除成员
  const removeMemberMutation = useMutation({
    mutationFn: (userId: string) => GroupsAPI.removeMember(groupId, userId),
    onSuccess: () => {
      toast.success('成员已移除')
      queryClient.invalidateQueries({ queryKey: groupsQueryKeys.members(groupId) })
      queryClient.invalidateQueries({ queryKey: groupsQueryKeys.detail(groupId) })
      setRemovingMember(null)
    },
    onError: (error: any) => {
      toast.error(error.response?.data?.message || '移除成员失败')
    },
  })

  const handleRemoveMember = (member: GroupMember) => {
    setRemovingMember(member)
  }

  const confirmRemoveMember = () => {
    if (removingMember) {
      removeMemberMutation.mutate(removingMember.user_id)
    }
  }

  // 定义表格列
  const columns: SimpleColumn<GroupMember>[] = [
    {
      id: 'member',
      header: '成员',
      cell: (member: GroupMember) => (
        <div className="flex items-center gap-3">
          <div className="h-8 w-8 bg-primary/10 rounded-full flex items-center justify-center">
            <span className="text-sm font-medium text-primary">
              {member.display_name.charAt(0).toUpperCase()}
            </span>
          </div>
          <div>
            <div className="flex items-center gap-2">
              <span className="font-medium text-sm">{member.display_name}</span>
              {member.role === 'creator' && (
                <Crown className="h-3 w-3 text-yellow-500" />
              )}
            </div>
            <div className="flex items-center gap-1 text-xs text-muted-foreground">
              <Mail className="h-3 w-3" />
              {member.email}
            </div>
          </div>
        </div>
      ),
      className: 'min-w-[200px]',
    },
    {
      id: 'role',
      header: '角色',
      cell: (member: GroupMember) => (
        <Badge variant={member.role === 'creator' ? 'default' : 'outline'}>
          {member.role === 'creator' ? '创建者' : '成员'}
        </Badge>
      ),
      className: 'w-[100px]',
    },
    {
      id: 'joined_at',
      header: '加入时间',
      cell: (member: GroupMember) => (
        <div className="flex items-center gap-1 text-sm text-muted-foreground">
          <Calendar className="h-3 w-3" />
          {new Date(member.joined_at).toLocaleDateString()}
        </div>
      ),
      className: 'w-[120px]',
    },
    {
      id: 'actions',
      header: '操作',
      cell: (member: GroupMember) => (
        isCreator && member.role !== 'creator' ? (
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button variant="ghost" size="sm" className="h-8 w-8 p-0">
                <MoreVertical className="h-4 w-4" />
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end">
              <DropdownMenuItem
                className="text-destructive"
                onClick={() => handleRemoveMember(member)}
              >
                <UserMinus className="mr-2 h-4 w-4" />
                移除成员
              </DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>
        ) : null
      ),
      className: 'w-[80px]',
    },
  ]

  const members = membersData?.members || []

  return (
    <div className="space-y-6">
      <DataTable
        columns={columns}
        data={members}
        isLoading={isLoading}
        emptyMessage="暂无成员"
        searchPlaceholder="搜索成员..."
        searchFields={['display_name', 'email']}
        showPagination={false}
        showToolbar={true}
        toolbarActions={
          <div className="flex items-center gap-2 text-sm text-muted-foreground">
            <Users className="h-4 w-4" />
            共 {membersData?.total || 0} 名成员
          </div>
        }
      />

      {/* 移除成员确认对话框 */}
      <AlertDialog
        open={!!removingMember}
        onOpenChange={() => setRemovingMember(null)}
      >
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>确认移除成员</AlertDialogTitle>
            <AlertDialogDescription>
              您确定要将 {removingMember?.display_name} 从分组中移除吗？
              移除后该成员将失去对分组内所有Topic的访问权限。
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel>取消</AlertDialogCancel>
            <AlertDialogAction
              onClick={confirmRemoveMember}
              className="bg-red-600 hover:bg-red-700"
              disabled={removeMemberMutation.isPending}
            >
              {removeMemberMutation.isPending ? '移除中...' : '确认移除'}
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </div>
  )
}