import { useState, useCallback, useMemo } from 'react'
import { DashboardLayout } from '@/components/layout/dashboard-layout'
import { DataTable } from '@/components/custom_components/data-table'
import { Button } from '@/components/ui/button'
import { Plus } from 'lucide-react'
import { columns } from './components/device-types-columns'
import { DeviceTypesDialogs } from './components/device-types-dialogs'
import DeviceTypesProvider from './context/device-types-context'
import { useDeviceTypes as useDeviceTypesHook } from './hooks/use-device-types'
import { GetDeviceTypesParams } from '@/api/devices'
import { useDeviceTypes } from './context/device-types-context'

function DeviceTypesPrimaryButtons() {
  const { openDialog } = useDeviceTypes()

  return (
    <div className="flex items-center space-x-2">
      <Button onClick={() => openDialog('add')}>
        <Plus className="mr-2 h-4 w-4" />
        添加设备类型
      </Button>
    </div>
  )
}

function DeviceTypesPageContent() {
  // 简化的状态管理
  const [params, setParams] = useState<GetDeviceTypesParams>({
    page: 1,
    page_size: 10,
  })

  // 获取设备类型数据
  const { data: deviceTypesResponse, isLoading, error } = useDeviceTypesHook(params)

  // 处理参数更新
  const handleParamsChange = useCallback((newParams: GetDeviceTypesParams) => {
    setParams(prev => ({ ...prev, ...newParams }))
  }, [])

  // 处理分页
  const handlePageChange = useCallback((page: number) => {
    handleParamsChange({ page })
  }, [handleParamsChange])

  const handlePageSizeChange = useCallback((pageSize: number) => {
    handleParamsChange({ page: 1, page_size: pageSize })
  }, [handleParamsChange])

  // 处理搜索
  const handleSearchChange = useCallback((search: string) => {
    // 设备类型API暂不支持搜索，这里只是为了保持接口一致性
    // 可以在客户端进行搜索筛选
  }, [])

  // 分页信息
  const paginationInfo = useMemo(() => {
    if (!deviceTypesResponse) return undefined
    return {
      currentPage: deviceTypesResponse.page,
      totalPages: Math.ceil(deviceTypesResponse.total / deviceTypesResponse.size),
      pageSize: deviceTypesResponse.size,
      totalItems: deviceTypesResponse.total,
    }
  }, [deviceTypesResponse])

  // 转换设备类型数据
  const transformedDeviceTypes = useMemo(() => {
    return deviceTypesResponse?.device_types || []
  }, [deviceTypesResponse])

  return (
    <DashboardLayout
      headerFixed={true}
      pageHeader={{
        title: '设备类型管理',
        description: '管理系统中的设备类型，包括预定义类型和自定义类型。',
        actions: <DeviceTypesPrimaryButtons />
      }}
    >
      <div className='-mx-4 flex-1 overflow-auto px-4 py-1 lg:flex-row lg:space-y-0 lg:space-x-12'>
        <DataTable
          columns={columns}
          data={transformedDeviceTypes}
          isLoading={isLoading}
          emptyMessage="暂无设备类型数据。"
          searchPlaceholder="搜索设备类型名称或描述..."
          searchFields={['name', 'description']}
          serverSidePagination={true}
          serverPaginationInfo={paginationInfo}
          onServerPageChange={handlePageChange}
          onServerPageSizeChange={handlePageSizeChange}
          onServerSearchChange={handleSearchChange}
          // 设备类型暂不支持服务端筛选
          serverSearchValue=""
          serverSelectedFilters={{}}
        />
      </div>

      <DeviceTypesDialogs />
    </DashboardLayout>
  )
}

export default function DeviceTypesPage() {
  return (
    <DeviceTypesProvider>
      <DeviceTypesPageContent />
    </DeviceTypesProvider>
  )
}
