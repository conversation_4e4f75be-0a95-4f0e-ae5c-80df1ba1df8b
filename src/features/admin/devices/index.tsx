import { useState, useCallback, useMemo } from 'react'
import { DashboardLayout } from '@/components/layout/dashboard-layout'
import { DataTable } from '@/components/custom_components/data-table'
import { columns } from './components/devices-columns'
import { DevicesDialogs } from './components/devices-dialogs'
import { DevicesPrimaryButtons } from './components/devices-primary-buttons'
import DevicesProvider from './context/devices-context'
import { useDevices as useDevicesHook } from './hooks/use-devices'
import { GetDevicesParams, DeviceStatus } from '@/api/devices'
import { Device } from './data/schema'
import { predefinedDeviceTypes, deviceStatuses } from './data/data'

function DevicesPageContent() {
  // 简化的状态管理
  const [params, setParams] = useState<GetDevicesParams>({
    page: 1,
    page_size: 10,
  })

  // 筛选器状态
  const [filterValues, setFilterValues] = useState({
    search: '',
    device_type: 'all' as string | 'all',
    status: 'all' as DeviceStatus | 'all',
  })

  // 构建当前筛选状态用于显示
  const currentServerFilters = useMemo(() => {
    const filters: Record<string, string[]> = {}
    if (params.device_type) {
      filters.device_type = [params.device_type]
    }
    if (params.status) {
      filters.status = [params.status]
    }
    return filters
  }, [params.device_type, params.status])

  // 获取设备数据
  const { data: devicesResponse, isLoading, error } = useDevicesHook(params)

  // 处理参数更新
  const handleParamsChange = useCallback((newParams: GetDevicesParams) => {
    setParams(prev => ({ ...prev, ...newParams }))
  }, [])

  // 处理分页
  const handlePageChange = useCallback((page: number) => {
    handleParamsChange({ page })
  }, [handleParamsChange])

  const handlePageSizeChange = useCallback((pageSize: number) => {
    handleParamsChange({ page: 1, page_size: pageSize })
  }, [handleParamsChange])

  // 处理搜索
  const handleSearchChange = useCallback((search: string) => {
    const newParams: GetDevicesParams = { page: 1 }
    if (search && search.trim()) {
      newParams.search = search.trim()
    }
    handleParamsChange(newParams)
  }, [handleParamsChange])

  // 分页信息
  const paginationInfo = useMemo(() => {
    if (!devicesResponse) return undefined
    return {
      currentPage: devicesResponse.page,
      totalPages: Math.ceil(devicesResponse.total / devicesResponse.size),
      pageSize: devicesResponse.size,
      totalItems: devicesResponse.total,
    }
  }, [devicesResponse])

  // 转换设备数据
  const transformedDevices = useMemo(() => {
    return devicesResponse?.devices || []
  }, [devicesResponse])

  return (
    <DashboardLayout
      headerFixed={true}
      pageHeader={{
        title: '设备管理',
        description: '管理您的设备信息，包括设备类型、状态等。',
        actions: <DevicesPrimaryButtons />
      }}
    >
      <div className='-mx-4 flex-1 overflow-auto px-4 py-1 lg:flex-row lg:space-y-0 lg:space-x-12'>
        <DataTable
          columns={columns}
          data={transformedDevices}
          isLoading={isLoading}
          emptyMessage="暂无设备数据。"
          searchPlaceholder="搜索设备名称或序列号..."
          searchFields={['device_name', 'serial_number', 'imei_code']}
          filters={[
            {
              id: 'device_type',
              title: '设备类型',
              options: predefinedDeviceTypes.map(type => ({
                label: `${type.label} - ${type.description}`,
                value: type.value,
              }))
            },
            {
              id: 'status',
              title: '状态',
              options: deviceStatuses.map(status => ({
                label: status.label,
                value: status.value,
              }))
            }
          ]}
          serverSidePagination={true}
          serverPaginationInfo={paginationInfo}
          onServerPageChange={handlePageChange}
          onServerPageSizeChange={handlePageSizeChange}
          onServerSearchChange={handleSearchChange}
          onServerFilterChange={(filterId, value) => {
            const newParams: GetDevicesParams = { page: 1 }
            
            if (filterId === 'device_type') {
              if (value && value !== 'all') {
                newParams.device_type = value
              } else {
                // 清除device_type筛选
                setParams(prev => {
                  const { device_type, ...rest } = prev
                  return { ...rest, page: 1 }
                })
                return
              }
            }
            if (filterId === 'status') {
              if (value && value !== 'all') {
                newParams.status = value as DeviceStatus
              } else {
                // 清除status筛选
                setParams(prev => {
                  const { status, ...rest } = prev
                  return { ...rest, page: 1 }
                })
                return
              }
            }
            handleParamsChange(newParams)
          }}
          // 传递当前筛选状态
          serverSearchValue={params.search || ''}
          serverSelectedFilters={currentServerFilters}
        />
      </div>

      <DevicesDialogs />
    </DashboardLayout>
  )
}

export default function DevicesPage() {
  return (
    <DevicesProvider>
      <DevicesPageContent />
    </DevicesProvider>
  )
}
