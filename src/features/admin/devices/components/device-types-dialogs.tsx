import { Dialog<PERSON><PERSON><PERSON>, DialogConfig } from '@/hooks/use-dialog-manager'
import { useDeviceTypes } from '../context/device-types-context'
import { DeviceType } from '../data/schema'
import { DeviceTypesActionDialog } from './device-types-action-dialog'
import { DeviceTypesDeleteDialog } from './device-types-delete-dialog'

type DeviceTypesDialogType = 'add' | 'edit' | 'delete'

// 配置所有对话框
const dialogConfigs: DialogConfig<DeviceTypesDialogType, DeviceType>[] = [
  {
    key: 'device-type-add',
    type: 'add',
    component: DeviceTypesActionDialog,
    requiresRow: false,
  },
  {
    key: 'device-type-edit',
    type: 'edit',
    component: DeviceTypesActionDialog,
    requiresRow: true,
  },
  {
    key: 'device-type-delete',
    type: 'delete',
    component: DeviceTypesDeleteDialog,
    requiresRow: true,
  },
]

export function DeviceTypesDialogs() {
  const { open, setOpen, currentRow, setCurrentRow } = useDeviceTypes()

  return (
    <DialogRenderer
      dialogs={dialogConfigs}
      open={open}
      setOpen={setOpen}
      currentRow={currentRow}
      setCurrentRow={setCurrentRow}
    />
  )
}
