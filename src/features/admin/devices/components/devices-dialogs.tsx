import { Dialog<PERSON><PERSON>er, DialogConfig } from '@/hooks/use-dialog-manager'
import { useDevices } from '../context/devices-context'
import { Device } from '../data/schema'
import { DevicesActionDialog } from './devices-action-dialog'
import { DevicesDeleteDialog } from './devices-delete-dialog'

type DevicesDialogType = 'add' | 'edit' | 'delete'

// 配置所有对话框
const dialogConfigs: DialogConfig<DevicesDialogType, Device>[] = [
  {
    key: 'device-add',
    type: 'add',
    component: DevicesActionDialog,
    requiresRow: false,
  },
  {
    key: 'device-edit',
    type: 'edit',
    component: DevicesActionDialog,
    requiresRow: true,
  },
  {
    key: 'device-delete',
    type: 'delete',
    component: DevicesDeleteDialog,
    requiresRow: true,
  },
]

export function DevicesDialogs() {
  const { open, setOpen, currentRow, setCurrentRow } = useDevices()

  return (
    <DialogRenderer
      dialogs={dialogConfigs}
      open={open}
      setOpen={setOpen}
      currentRow={currentRow}
      setCurrentRow={setCurrentRow}
    />
  )
}
