import { SimpleColumn } from '@/components/simple-table'
import { Button } from '@/components/ui/button'
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu'
import { MoreHorizontal, Edit, Trash2 } from 'lucide-react'
import { format } from 'date-fns'
import { DeviceType } from '../data/schema'
import { useDeviceTypes } from '../context/device-types-context'

export const columns: SimpleColumn<DeviceType>[] = [
  {
    id: 'name',
    accessorKey: 'name',
    header: '设备类型名称',
    cell: (deviceType: DeviceType) => {
      return (
        <span className="font-medium">{deviceType.name}</span>
      )
    },
  },
  {
    id: 'description',
    accessorKey: 'description',
    header: '描述',
    cell: (deviceType: DeviceType) => {
      return (
        <span className="text-sm text-muted-foreground">{deviceType.description}</span>
      )
    },
  },
  {
    id: 'created_at',
    accessorKey: 'created_at',
    header: '创建时间',
    cell: (deviceType: DeviceType) => {
      const date = new Date(deviceType.created_at)
      return (
        <div className="flex flex-col">
          <span className="text-sm">{format(date, 'yyyy-MM-dd')}</span>
          <span className="text-xs text-muted-foreground">{format(date, 'HH:mm:ss')}</span>
        </div>
      )
    },
  },
  {
    id: 'updated_at',
    accessorKey: 'updated_at',
    header: '更新时间',
    cell: (deviceType: DeviceType) => {
      const date = new Date(deviceType.updated_at)
      return (
        <div className="flex flex-col">
          <span className="text-sm">{format(date, 'yyyy-MM-dd')}</span>
          <span className="text-xs text-muted-foreground">{format(date, 'HH:mm:ss')}</span>
        </div>
      )
    },
  },
  {
    id: 'actions',
    header: '操作',
    cell: (deviceType: DeviceType) => {
      const { openDialog } = useDeviceTypes()

      return (
        <DropdownMenu>
          <DropdownMenuTrigger asChild>
            <Button variant="ghost" className="h-8 w-8 p-0">
              <span className="sr-only">打开菜单</span>
              <MoreHorizontal className="h-4 w-4" />
            </Button>
          </DropdownMenuTrigger>
          <DropdownMenuContent align="end">
            <DropdownMenuItem
              onClick={() => openDialog('edit', deviceType)}
              className="cursor-pointer"
            >
              <Edit className="mr-2 h-4 w-4" />
              编辑
            </DropdownMenuItem>
            <DropdownMenuItem
              onClick={() => openDialog('delete', deviceType)}
              className="cursor-pointer text-red-600"
            >
              <Trash2 className="mr-2 h-4 w-4" />
              删除
            </DropdownMenuItem>
          </DropdownMenuContent>
        </DropdownMenu>
      )
    },
  },
]
