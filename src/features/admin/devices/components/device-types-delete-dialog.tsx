import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from '@/components/ui/alert-dialog'
import { DeviceType } from '../data/schema'
import { useDeleteDeviceType } from '../hooks/use-device-types'
import { useDeviceTypes } from '../context/device-types-context'

interface DeviceTypesDeleteDialogProps {
  open: boolean
  onOpenChange: (open: boolean) => void
  currentRow?: DeviceType
}

export function DeviceTypesDeleteDialog({
  open,
  onOpenChange,
  currentRow,
}: DeviceTypesDeleteDialogProps) {
  const { closeDialog } = useDeviceTypes()
  const deleteDeviceType = useDeleteDeviceType()
  const deviceType = currentRow

  const handleDelete = async () => {
    if (!deviceType) return

    try {
      await deleteDeviceType.mutateAsync(deviceType.id)
      closeDialog()
    } catch (error) {
      // 错误处理已在hooks中完成
    }
  }

  return (
    <AlertDialog open={open} onOpenChange={onOpenChange}>
      <AlertDialogContent>
        <AlertDialogHeader>
          <AlertDialogTitle>确认删除设备类型</AlertDialogTitle>
          <AlertDialogDescription>
            您确定要删除设备类型 "{deviceType?.name}" 吗？
            <br />
            <span className="text-sm text-muted-foreground">
              描述：{deviceType?.description}
            </span>
            <br />
            <br />
            <span className="text-red-600 font-medium">
              此操作无法撤销。如果有设备正在使用此设备类型，删除操作将失败。
            </span>
          </AlertDialogDescription>
        </AlertDialogHeader>
        <AlertDialogFooter>
          <AlertDialogCancel disabled={deleteDeviceType.isPending}>
            取消
          </AlertDialogCancel>
          <AlertDialogAction
            onClick={handleDelete}
            disabled={deleteDeviceType.isPending}
            className="bg-red-600 hover:bg-red-700"
          >
            {deleteDeviceType.isPending ? '删除中...' : '确认删除'}
          </AlertDialogAction>
        </AlertDialogFooter>
      </AlertDialogContent>
    </AlertDialog>
  )
}
