import {
  Smartphone,
  Wifi,
  Settings,
  MapPin,
  CheckCircle,
  XCircle,
  AlertTriangle,
} from 'lucide-react'
import { DeviceStatus } from './schema'

// 预定义设备类型
export const predefinedDeviceTypes = [
  {
    value: 'BC-3GM-R',
    label: 'BC-3GM-R',
    description: '3G模块设备',
    icon: Smartphone,
  },
  {
    value: 'BC-4GM-C',
    label: 'BC-4GM-C',
    description: '4G模块设备',
    icon: Wifi,
  },
  {
    value: 'BC-ECR-C',
    label: 'BC-ECR-C',
    description: 'ECR控制器设备',
    icon: Settings,
  },
  {
    value: 'BGTR',
    label: 'BGTR',
    description: 'GPS追踪器设备',
    icon: MapPin,
  },
]

// 设备状态配置
export const deviceStatuses = [
  {
    value: 'active' as DeviceStatus,
    label: '正常',
    icon: CheckCircle,
    color: 'text-green-600',
    bgColor: 'bg-green-100',
    borderColor: 'border-green-200',
  },
  {
    value: 'inactive' as DeviceStatus,
    label: '停用',
    icon: XCircle,
    color: 'text-gray-600',
    bgColor: 'bg-gray-100',
    borderColor: 'border-gray-200',
  },
  {
    value: 'maintenance' as DeviceStatus,
    label: '维护',
    icon: AlertTriangle,
    color: 'text-yellow-600',
    bgColor: 'bg-yellow-100',
    borderColor: 'border-yellow-200',
  },
]

// 状态样式映射
export const statusStyles = {
  active: {
    label: '正常',
    className: 'bg-green-100 text-green-800 border-green-200',
  },
  inactive: {
    label: '停用',
    className: 'bg-gray-100 text-gray-800 border-gray-200',
  },
  maintenance: {
    label: '维护',
    className: 'bg-yellow-100 text-yellow-800 border-yellow-200',
  },
}

// 状态标签映射
export const statusLabels: Record<DeviceStatus, string> = {
  active: '正常',
  inactive: '停用',
  maintenance: '维护',
}

// 设备类型标签映射
export const deviceTypeLabels: Record<string, string> = {
  'BC-3GM-R': '3G模块设备',
  'BC-4GM-C': '4G模块设备',
  'BC-ECR-C': 'ECR控制器设备',
  'BGTR': 'GPS追踪器设备',
}
