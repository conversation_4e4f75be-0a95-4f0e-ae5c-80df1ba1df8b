import { useMutation, useQuery, useQueryClient } from '@/hooks/use-api'
import { toast } from 'sonner'
import {
  DeviceTypesAPI,
  GetDeviceTypesParams,
  CreateDeviceTypeRequest,
  UpdateDeviceTypeRequest
} from '@/api/devices'

export type { CreateDeviceTypeRequest, UpdateDeviceTypeRequest }

// 查询键
const DEVICE_TYPES_QUERY_KEY = 'device-types'

// 获取设备类型列表
export function useDeviceTypes(params?: GetDeviceTypesParams) {
  return useQuery({
    queryKey: [DEVICE_TYPES_QUERY_KEY, params],
    queryFn: () => DeviceTypesAPI.getDeviceTypes(params),
    select: (data) => data.data,
  })
}

// 获取单个设备类型
export function useDeviceType(id: string) {
  return useQuery({
    queryKey: [DEVICE_TYPES_QUERY_KEY, id],
    queryFn: () => DeviceTypesAPI.getDeviceType(id),
    select: (data) => data.data,
    enabled: !!id,
  })
}

// 创建设备类型
export function useCreateDeviceType() {
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: (data: CreateDeviceTypeRequest) => DeviceTypesAPI.createDeviceType(data),
    onSuccess: (response) => {
      queryClient.invalidateQueries({ queryKey: [DEVICE_TYPES_QUERY_KEY] })
      toast.success(response.message || '设备类型创建成功')
    },
    onError: (error: any) => {
      const message = error?.response?.data?.message || error?.message || '创建设备类型失败'
      toast.error(message)
    },
  })
}

// 更新设备类型
export function useUpdateDeviceType() {
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: ({ id, data }: { id: string; data: UpdateDeviceTypeRequest }) =>
      DeviceTypesAPI.updateDeviceType(id, data),
    onSuccess: (response) => {
      queryClient.invalidateQueries({ queryKey: [DEVICE_TYPES_QUERY_KEY] })
      toast.success(response.message || '设备类型更新成功')
    },
    onError: (error: any) => {
      const message = error?.response?.data?.message || error?.message || '更新设备类型失败'
      toast.error(message)
    },
  })
}

// 删除设备类型
export function useDeleteDeviceType() {
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: (id: string) => DeviceTypesAPI.deleteDeviceType(id),
    onSuccess: (response) => {
      queryClient.invalidateQueries({ queryKey: [DEVICE_TYPES_QUERY_KEY] })
      toast.success(response.message || '设备类型删除成功')
    },
    onError: (error: any) => {
      const message = error?.response?.data?.message || error?.message || '删除设备类型失败'
      toast.error(message)
    },
  })
}
