import { SimpleColumn } from '@/components/simple-table'
import { cn } from '@/lib/utils'
import { Badge } from '@/components/ui/badge'
import LongText from '@/components/custom_components/long-text'
import {
  statusStyles,
  statusLabels,
  userTypes,
} from '@/features/admin/users/data/data'
import { User, UserRole } from '@/features/admin/users/data/schema'

import { DataTableRowActions } from './data-table-row-actions'

// 预计算角色映射，避免运行时查找
const roleMap = new Map<
  UserRole,
  { label: string; value: UserRole; icon: React.ElementType }
>(userTypes.map(type => [type.value, type]))

export const columns: SimpleColumn<User>[] = [
  {
    id: 'fullName',
    header: 'Name',
    cell: (user: User) => {
      const firstName = user.profile?.first_name || ''
      const lastName = user.profile?.last_name || ''
      const displayName = user.profile?.display_name

      // 优先显示display_name，否则显示first_name + last_name
      const fullName = displayName || `${firstName} ${lastName}`.trim() || 'N/A'

      return <LongText className='max-w-36'>{fullName}</LongText>
    },
    className: 'w-36',
    sortable: true,
  },
  {
    id: 'email',
    header: 'Email',
    accessorKey: 'email',
    cell: (user: User) => (
      <div className='w-fit text-nowrap'>{user.email}</div>
    ),
    sortable: true,
  },
  {
    id: 'company',
    header: 'Company',
    cell: (user: User) => {
      const company = user.profile?.company
      return <div>{company || 'N/A'}</div>
    },
    sortable: false,
  },
  {
    id: 'department',
    header: 'Department',
    cell: (user: User) => {
      const department = user.profile?.department
      return <div>{department || 'N/A'}</div>
    },
    sortable: false,
  },
  {
    id: 'status',
    header: 'Status',
    accessorKey: 'status',
    cell: (user: User) => {
      const { status } = user
      const badgeColor = statusStyles.get(status)
      const statusLabel = statusLabels.get(status) || status
      return (
        <div className='flex space-x-2'>
          <Badge variant='outline' className={cn('capitalize', badgeColor)}>
            {statusLabel}
          </Badge>
        </div>
      )
    },
    sortable: false,
  },
  {
    id: 'role',
    header: 'Role',
    accessorKey: 'role',
    cell: (user: User) => {
      const { role } = user
      const userType = roleMap.get(role)

      if (!userType) {
        return null
      }

      return (
        <div className='flex items-center gap-x-2'>
          {userType.icon && (
            <userType.icon size={16} className='text-muted-foreground' />
          )}
          <span className='text-sm capitalize'>{role}</span>
        </div>
      )
    },
    sortable: false,
  },
  {
    id: 'actions',
    header: 'Actions',
    cell: (user: User) => <DataTableRowActions user={user} />,
    sortable: false,
  },
]
