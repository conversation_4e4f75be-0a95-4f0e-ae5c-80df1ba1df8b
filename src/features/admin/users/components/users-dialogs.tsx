import { Dialog<PERSON><PERSON><PERSON>, DialogConfig } from '@/hooks/use-dialog-manager'
import { useUsers } from '../context/users-context'
import { User } from '../data/schema'
import { UsersActionDialog } from './users-action-dialog'
import { UsersDeleteDialog } from './users-delete-dialog'

type UsersDialogType = 'add' | 'edit' | 'delete'

// 配置所有对话框
const dialogConfigs: DialogConfig<UsersDialogType, User>[] = [
  {
    key: 'user-add',
    type: 'add',
    component: UsersActionDialog,
    requiresRow: false,
  },
  {
    key: 'user-edit',
    type: 'edit',
    component: UsersActionDialog,
    requiresRow: true,
  },
  {
    key: 'user-delete',
    type: 'delete',
    component: UsersDeleteDialog,
    requiresRow: true,
  },
]

export function UsersDialogs() {
  const { open, setOpen, currentRow, setCurrentRow } = useUsers()

  return (
    <DialogRenderer
      dialogs={dialogConfigs}
      open={open}
      setOpen={setOpen}
      currentRow={currentRow}
      setCurrentRow={setCurrentRow}
    />
  )
}
