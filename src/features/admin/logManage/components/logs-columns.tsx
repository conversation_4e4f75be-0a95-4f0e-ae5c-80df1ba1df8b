import { SimpleColumn } from '@/components/simple-table'
import { cn } from '@/lib/utils'
import { Badge } from '@/components/ui/badge'
import LongText from '@/components/custom_components/long-text'
import {
  levelStyles,
  levelLabels,
  moduleLabels,
  actionLabels,
  logLevels,
  logModules,
  logActions,
} from '@/features/admin/logManage/data/data'
import { LogEntry } from '@/features/admin/logManage/data/schema'

// 预计算映射，避免运行时查找
const levelMap = new Map(logLevels.map(level => [level.value, level]))
const moduleMap = new Map(logModules.map(module => [module.value, module]))
const actionMap = new Map(logActions.map(action => [action.value, action]))

export const columns: SimpleColumn<LogEntry>[] = [
  {
    id: 'timestamp',
    header: 'Time',
    accessorKey: 'timestamp',
    cell: (log: LogEntry) => {
      const date = new Date(log.timestamp)
      return (
        <div className='text-sm text-nowrap'>
          {date.toISOString()}
        </div>
      )
    },
    sortable: true,
    width: '180px',
  },
  {
    id: 'level',
    header: 'Level',
    accessorKey: 'level',
    cell: (log: LogEntry) => {
      const { level } = log
      const badgeColor = levelStyles.get(level)
      const levelLabel = levelLabels.get(level) || level
      return (
        <Badge variant='outline' className={cn('capitalize', badgeColor)}>
          {levelLabel}
        </Badge>
      )
    },
    sortable: false,
    width: '100px',
  },
  {
    id: 'module',
    header: 'Module',
    accessorKey: 'module',
    cell: (log: LogEntry) => {
      const { module } = log
      const moduleLabel = moduleLabels.get(module) || module
      return <div className='text-sm capitalize'>{moduleLabel}</div>
    },
    sortable: false,
    width: '120px',
  },
  {
    id: 'action',
    header: 'Action',
    accessorKey: 'action',
    cell: (log: LogEntry) => {
      const { action } = log
      const actionLabel = actionLabels.get(action) || action
      return <div className='text-sm'>{actionLabel}</div>
    },
    sortable: false,
    width: '150px',
  },
  {
    id: 'message',
    header: 'Message',
    accessorKey: 'message',
    cell: (log: LogEntry) => (
      <LongText className='max-w-96'>{log.message}</LongText>
    ),
    sortable: false,
  },
  {
    id: 'user',
    header: 'User',
    accessorKey: 'user_id',
    cell: (log: LogEntry) => {
      const userId = log.user_id
      return (
        <div className='text-sm'>
          <Badge variant='outline' className='text-blue-600 bg-blue-50 border-blue-200'>
            {userId ? `${userId}` : 'System'}
          </Badge>
        </div>
      )
    },
    sortable: false,
    width: '100px',
  },
  {
    id: 'ip',
    header: 'IP Address',
    accessorKey: 'ip',
    cell: (log: LogEntry) => (
      <div className='text-sm'>
        {log.ip || 'N/A'}
      </div>
    ),
    sortable: false,
    width: '130px',
  },
]