import * as React from 'react'
import {
  SimpleTable,
  SimpleColumn,
  useSimpleTable,
  SimplePagination,
  SimpleToolbar,
  FilterConfig
} from '@/components/simple-table'

// 服务端分页信息
export interface ServerPaginationInfo {
  currentPage: number
  totalPages: number
  pageSize: number
  totalItems: number
}

// 简化的数据表格属性
export interface DataTableProps<TData> {
  columns: SimpleColumn<TData>[]
  data: TData[]
  emptyMessage?: string
  className?: string
  isLoading?: boolean
  serverSidePagination?: boolean
  // 搜索配置
  searchPlaceholder?: string
  searchFields?: (keyof TData)[]
  // 筛选配置
  filters?: FilterConfig[]
  // 分页配置
  pageSize?: number
  showPagination?: boolean
  // 工具栏配置
  showToolbar?: boolean
  toolbarActions?: React.ReactNode
  // 服务端分页配置
  serverPaginationInfo?: ServerPaginationInfo
  onServerPageChange?: (page: number) => void
  onServerPageSizeChange?: (pageSize: number) => void
  onServerSearchChange?: (search: string) => void
  onServerFilterChange?: (filterId: string, value: string) => void
  // 服务端筛选状态
  serverSearchValue?: string
  serverSelectedFilters?: Record<string, string[]>
}

export function DataTable<TData>({
  columns,
  data,
  emptyMessage = 'No results.',
  className,
  isLoading = false,
  serverSidePagination = false,
  searchPlaceholder = 'Search...',
  searchFields = [],
  filters = [],
  pageSize = 10,
  showPagination = true,
  showToolbar = true,
  toolbarActions,
  // 服务端分页相关
  serverPaginationInfo,
  onServerPageChange,
  onServerPageSizeChange,
  onServerSearchChange,
  onServerFilterChange,
  // 服务端筛选状态
  serverSearchValue = '',
  serverSelectedFilters = {},
}: DataTableProps<TData>) {
  // 根据是否为服务端分页选择不同的逻辑
  const clientSideTable = useSimpleTable({
    data,
    initialPageSize: pageSize,
    serverSide: false,
    // 添加自定义筛选函数来支持搜索字段
    filterFn: (item, filters, search) => {
      // 搜索筛选
      if (search && searchFields.length > 0) {
        const searchLower = search.toLowerCase()
        const matchesSearch = searchFields.some(field => {
          const value = getNestedValue(item, field as string)
          return String(value).toLowerCase().includes(searchLower)
        })
        if (!matchesSearch) return false
      }

      // 其他筛选
      for (const [key, value] of Object.entries(filters)) {
        if (value && value !== 'all') {
          const itemValue = getNestedValue(item, key)
          if (String(itemValue) !== value) {
            return false
          }
        }
      }

      return true
    }
  })

  // 辅助函数：获取嵌套属性值
  const getNestedValue = (obj: any, path: string): any => {
    return path.split('.').reduce((current, key) => current?.[key], obj)
  }

  // 根据分页模式选择数据和处理函数
  const tableData = serverSidePagination ? data : clientSideTable.data
  const currentSort = serverSidePagination ? { column: '', direction: null } : clientSideTable.sort
  const paginationInfo = serverSidePagination ? serverPaginationInfo : clientSideTable.paginationInfo

  // 处理函数
  const handlePageChange = serverSidePagination
    ? (page: number) => onServerPageChange?.(page)
    : clientSideTable.goToPage

  const handlePageSizeChange = serverSidePagination
    ? (pageSize: number) => onServerPageSizeChange?.(pageSize)
    : clientSideTable.changePageSize

  const handleSearchChange = serverSidePagination
    ? (search: string) => onServerSearchChange?.(search)
    : clientSideTable.updateSearch

  const handleSortChange = serverSidePagination
    ? () => {} // 服务端排序暂不实现
    : (column: string) => {
        const newDirection =
          currentSort.column === column && currentSort.direction === 'asc'
            ? 'desc'
            : currentSort.column === column && currentSort.direction === 'desc'
            ? null
            : 'asc'
        clientSideTable.updateSort({ column, direction: newDirection })
      }



  // 处理筛选器变化
  const handleFilterChange = (filterId: string, values: string[]) => {
    const value = values[0] || ''
    if (serverSidePagination && onServerFilterChange) {
      onServerFilterChange(filterId, value)
    } else {
      clientSideTable.updateFilter(filterId, value)
    }
  }

  const handleClearFilters = () => {
    if (serverSidePagination) {
      // 服务端模式：触发所有筛选器重置
      filters.forEach(filter => {
        onServerFilterChange?.(filter.id, '')
      })
      onServerSearchChange?.('')
    } else {
      clientSideTable.resetFilters()
    }
  }

  // 获取当前筛选状态
  const currentFilters = serverSidePagination
    ? Object.fromEntries(
        Object.entries(serverSelectedFilters).map(([key, values]) => [
          key,
          values[0] || ''
        ])
      )
    : clientSideTable.filters

  const currentSearch = serverSidePagination
    ? serverSearchValue
    : clientSideTable.search

  return (
    <div className={`space-y-4 ${className || ''}`}>
      {showToolbar && (
        <SimpleToolbar
          searchValue={currentSearch}
          onSearchChange={handleSearchChange || (() => {})}
          searchPlaceholder={searchPlaceholder}
          filters={filters}
          selectedFilters={serverSidePagination
            ? serverSelectedFilters
            : Object.fromEntries(
                Object.entries(currentFilters).map(([key, value]) => [
                  key,
                  value ? [value] : []
                ])
              )
          }
          onFilterChange={handleFilterChange}
          onClearFilters={handleClearFilters}
        >
          {toolbarActions}
        </SimpleToolbar>
      )}

      <SimpleTable
        columns={columns}
        data={tableData}
        isLoading={isLoading}
        emptyMessage={emptyMessage}
        sortable={!serverSidePagination}
        onSort={handleSortChange}
        currentSort={currentSort}
        className="rounded-md border"
      />

      {showPagination && paginationInfo && paginationInfo.totalItems > 0 && (
        <SimplePagination
          currentPage={paginationInfo.currentPage}
          totalPages={paginationInfo.totalPages}
          pageSize={paginationInfo.pageSize}
          totalItems={paginationInfo.totalItems}
          onPageChange={handlePageChange}
          onPageSizeChange={handlePageSizeChange}
        />
      )}
    </div>
  )
}