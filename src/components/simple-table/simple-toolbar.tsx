import { SimpleSearch } from './simple-search'
import { SimpleFilters, FilterConfig } from './simple-filters'

interface SimpleToolbarProps {
  searchValue: string
  onSearchChange: (value: string) => void
  searchPlaceholder?: string
  filters?: FilterConfig[]
  selectedFilters?: Record<string, string[]>
  onFilterChange?: (filterId: string, values: string[]) => void
  onClearFilters?: () => void
  children?: React.ReactNode
}

export function SimpleToolbar({
  searchValue,
  onSearchChange,
  searchPlaceholder = 'Search...',
  filters = [],
  selectedFilters = {},
  onFilterChange,
  onClearFilters,
  children,
}: SimpleToolbarProps) {
  return (
    <div className="flex items-center justify-between">
      <div className="flex flex-1 items-center space-x-2">
        <SimpleSearch
          value={searchValue}
          onChange={onSearchChange}
          placeholder={searchPlaceholder}
          className="w-[150px] lg:w-[250px]"
        />
        {filters.length > 0 && onFilterChange && onClearFilters && (
          <SimpleFilters
            filters={filters}
            selectedFilters={selectedFilters}
            onFilterChange={onFilterChange}
            onClearFilters={onClearFilters}
          />
        )}
      </div>
      {children && <div className="flex items-center space-x-2">{children}</div>}
    </div>
  )
}
